import streamlit as st
import oracledb
import pandas as pd
import json
import re
from datetime import datetime, date
import os

# Page configuration
st.set_page_config(
    page_title="Oracle KPI Dashboard",
    page_icon="📊",
    layout="wide"
)

@st.cache_data
def load_config():
    """Load database configuration from config.json"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        st.error("Config file 'config.json' not found. Please create it with your database credentials.")
        st.stop()
    except json.JSONDecodeError:
        st.error("Invalid JSON in config.json file.")
        st.stop()

@st.cache_data
def parse_sql_file():
    """Parse the Oracle KPIs.sql file to extract queries and descriptions"""
    try:
        with open('Oracle KPIs.sql', 'r') as f:
            content = f.read()

        # Split content into blocks separated by empty lines
        blocks = re.split(r'\n\s*\n', content.strip())
        queries = []

        for block in blocks:
            if not block.strip():
                continue

            lines = block.strip().split('\n')
            description = ""
            sql_lines = []

            # Extract description from first comment line
            for line in lines:
                line = line.strip()
                if line.startswith('--') and not description:
                    description = line[2:].strip()
                elif not line.startswith('--') and line:
                    sql_lines.append(line)

            if sql_lines:
                sql = '\n'.join(sql_lines)
                if sql.strip():
                    queries.append({
                        'description': description if description else f"Query {len(queries) + 1}",
                        'sql': sql
                    })

        return queries
    except FileNotFoundError:
        st.error("SQL file 'Oracle KPIs.sql' not found.")
        st.stop()

def create_connection(config):
    """Create Oracle database connection"""
    try:
        # Check if protocol is specified (for TCPS support)
        protocol = config['database'].get('protocol', 'TCP')

        if protocol.upper() == 'TCPS':
            # For TCPS connections, create a full TNS descriptor
            dsn = f"""(DESCRIPTION=
                (ADDRESS=(PROTOCOL=TCPS)
                    (HOST={config['database']['host']})
                    (PORT={config['database']['port']}))
                (CONNECT_DATA=
                    (SERVER=DEDICATED)
                    (SERVICE_NAME={config['database']['service_name']})))"""
        else:
            # Standard TCP connection
            dsn = f"{config['database']['host']}:{config['database']['port']}/{config['database']['service_name']}"

        connection = oracledb.connect(
            user=config['database']['username'],
            password=config['database']['password'],
            dsn=dsn
        )
        return connection
    except oracledb.Error as e:
        st.error(f"Database connection error: {str(e)}")
        return None

def execute_query(connection, sql, from_date, to_date):
    """Execute SQL query with date parameters"""
    try:
        # Replace date placeholders
        formatted_sql = sql.replace('<from_date>', from_date.strftime('%Y-%m-%d'))
        formatted_sql = formatted_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))
        
        # Execute query
        cursor = connection.cursor()
        cursor.execute(formatted_sql)
        
        # Fetch results
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()
        
        cursor.close()
        
        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=columns)
        return df, formatted_sql
    except oracledb.Error as e:
        st.error(f"Query execution error: {str(e)}")
        return None, None

def main():
    st.title("📊 Oracle KPI Dashboard")
    st.markdown("---")
    
    # Load configuration and SQL queries
    config = load_config()
    queries = parse_sql_file()
    
    # Sidebar for inputs
    st.sidebar.header("Query Parameters")
    
    # Date selectors
    col1, col2 = st.sidebar.columns(2)
    with col1:
        from_date = st.date_input(
            "From Date",
            value=date.today().replace(day=1),  # Default to first day of current month
            key="from_date"
        )
    with col2:
        to_date = st.date_input(
            "To Date",
            value=date.today(),
            key="to_date"
        )
    
    # Validate date range
    if from_date > to_date:
        st.sidebar.error("From date must be before or equal to To date")
        return
    
    # Query selection
    query_options = [f"{i+1}. {q['description']}" for i, q in enumerate(queries)]
    selected_query_idx = st.sidebar.selectbox(
        "Select KPI Query",
        range(len(query_options)),
        format_func=lambda x: query_options[x]
    )
    
    # Display selected query info
    st.subheader(f"Selected Query: {queries[selected_query_idx]['description']}")
    
    # Show date range
    st.info(f"Date Range: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")
    
    # Run query button
    if st.button("🚀 Run Query", type="primary"):
        with st.spinner("Connecting to database and executing query..."):
            # Create database connection
            connection = create_connection(config)
            if connection is None:
                return
            
            try:
                # Execute selected query
                result_df, formatted_sql = execute_query(
                    connection, 
                    queries[selected_query_idx]['sql'], 
                    from_date, 
                    to_date
                )
                
                if result_df is not None:
                    st.success("✅ Query executed successfully!")
                    
                    # Display results
                    st.subheader("Query Results")
                    if len(result_df) > 0:
                        st.dataframe(result_df, use_container_width=True)
                        
                        # Show summary stats if numeric columns exist
                        numeric_cols = result_df.select_dtypes(include=['number']).columns
                        if len(numeric_cols) > 0:
                            st.subheader("Summary Statistics")
                            st.dataframe(result_df[numeric_cols].describe(), use_container_width=True)
                    else:
                        st.warning("Query returned no results.")
                    
                    # Show executed SQL (expandable)
                    with st.expander("View Executed SQL"):
                        st.code(formatted_sql, language="sql")
                
            finally:
                connection.close()

if __name__ == "__main__":
    main()
