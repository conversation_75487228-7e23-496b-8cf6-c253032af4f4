import streamlit as st
import oracledb
import pandas as pd
import json
import re
from datetime import datetime, date
import os

# Page configuration
st.set_page_config(
    page_title="Oracle KPI Dashboard",
    page_icon="📊",
    layout="wide"
)

@st.cache_data
def load_config():
    """Load database configuration from config.json"""
    try:
        with open('config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        st.error("Config file 'config.json' not found. Please create it with your database credentials.")
        st.stop()
    except json.JSONDecodeError:
        st.error("Invalid JSON in config.json file.")
        st.stop()

@st.cache_data
def parse_sql_file():
    """Parse the Oracle KPIs.sql file to extract queries and descriptions"""
    try:
        with open('Oracle KPIs.sql', 'r') as f:
            content = f.read()

        # Split content into blocks separated by empty lines
        blocks = re.split(r'\n\s*\n', content.strip())
        queries = []

        for block in blocks:
            if not block.strip():
                continue

            lines = block.strip().split('\n')
            description = ""
            sql_lines = []

            # Extract description from first comment line
            for line in lines:
                line = line.strip()
                if line.startswith('--') and not description:
                    description = line[2:].strip()
                elif not line.startswith('--') and line:
                    sql_lines.append(line)

            if sql_lines:
                sql = '\n'.join(sql_lines)
                if sql.strip():
                    queries.append({
                        'description': description if description else f"Query {len(queries) + 1}",
                        'sql': sql
                    })

        return queries
    except FileNotFoundError:
        st.error("SQL file 'Oracle KPIs.sql' not found.")
        st.stop()

def create_connection(config):
    """Create Oracle database connection"""
    try:
        # Check if protocol is specified (for TCPS support)
        protocol = config['database'].get('protocol', 'TCP')

        if protocol.upper() == 'TCPS':
            # For TCPS connections, create a full TNS descriptor
            dsn = f"""(DESCRIPTION=
                (ADDRESS=(PROTOCOL=TCPS)
                    (HOST={config['database']['host']})
                    (PORT={config['database']['port']}))
                (CONNECT_DATA=
                    (SERVER=DEDICATED)
                    (SERVICE_NAME={config['database']['service_name']})))"""
        else:
            # Standard TCP connection
            dsn = f"{config['database']['host']}:{config['database']['port']}/{config['database']['service_name']}"

        connection = oracledb.connect(
            user=config['database']['username'],
            password=config['database']['password'],
            dsn=dsn
        )
        return connection
    except oracledb.Error as e:
        st.error(f"Database connection error: {str(e)}")
        return None

def execute_query(connection, sql, from_date, to_date):
    """Execute SQL query with date parameters"""
    try:
        # Replace date placeholders
        formatted_sql = sql.replace('<from_date>', from_date.strftime('%Y-%m-%d'))
        formatted_sql = formatted_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))
        
        # Execute query
        cursor = connection.cursor()
        cursor.execute(formatted_sql)
        
        # Fetch results
        columns = [desc[0] for desc in cursor.description]
        rows = cursor.fetchall()
        
        cursor.close()
        
        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=columns)
        return df, formatted_sql
    except oracledb.Error as e:
        st.error(f"Query execution error: {str(e)}")
        return None, None

def run_single_query_function(query, from_date, to_date, config):
    """Execute a single query and display results"""
    # Show the running query prominently
    st.subheader("🔄 Executing Query")
    preview_sql = query['sql'].replace('<from_date>', from_date.strftime('%Y-%m-%d'))
    preview_sql = preview_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))

    st.info("**Running the following SQL query:**")
    st.code(preview_sql, language="sql")

    # Create progress placeholder
    progress_placeholder = st.empty()

    # Step 1: Connecting
    progress_placeholder.info("🔌 Connecting to Oracle database...")
    connection = create_connection(config)
    if connection is None:
        return

    try:
        # Step 2: Executing query
        progress_placeholder.info("⚡ Executing SQL query...")
        result_df, formatted_sql = execute_query(connection, query['sql'], from_date, to_date)

        if result_df is not None:
            # Step 3: Success
            progress_placeholder.success("✅ Query executed successfully!")

            # Display results
            st.subheader("📊 Query Results")
            if len(result_df) > 0:
                # Show row count
                st.info(f"📋 Retrieved {len(result_df)} row(s)")
                st.dataframe(result_df, use_container_width=True)

                # Show summary stats if numeric columns exist
                numeric_cols = result_df.select_dtypes(include=['number']).columns
                if len(numeric_cols) > 0:
                    st.subheader("📈 Summary Statistics")
                    st.dataframe(result_df[numeric_cols].describe(), use_container_width=True)
            else:
                st.warning("Query returned no results.")
        else:
            progress_placeholder.error("❌ Query execution failed!")

    except Exception as e:
        progress_placeholder.error(f"❌ Error: {str(e)}")
    finally:
        connection.close()

def run_all_queries_function(queries, from_date, to_date, config):
    """Execute all queries sequentially and display combined results"""
    st.subheader("🔄 Executing All Queries")

    # Create placeholders for progress and results
    progress_placeholder = st.empty()
    results_placeholder = st.empty()

    # Initialize combined results
    all_results = []

    # Step 1: Connecting
    progress_placeholder.info("🔌 Connecting to Oracle database...")
    connection = create_connection(config)
    if connection is None:
        return

    try:
        for i, query in enumerate(queries):
            # Update progress with current query details
            progress_placeholder.info(f"⚡ Executing query {i+1}/{len(queries)}: **{query['description']}**")

            # Show current query being executed
            current_sql = query['sql'].replace('<from_date>', from_date.strftime('%Y-%m-%d'))
            current_sql = current_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))

            with st.expander(f"📋 Currently Running Query {i+1}", expanded=False):
                st.code(current_sql, language="sql")

            # Execute query
            result_df, formatted_sql = execute_query(connection, query['sql'], from_date, to_date)

            if result_df is not None and len(result_df) > 0:
                # Add query description as first column
                result_df.insert(0, 'KPI_Description', query['description'])
                all_results.append(result_df)

                # Update results display with current progress
                if all_results:
                    combined_df = pd.concat(all_results, ignore_index=True)
                    with results_placeholder.container():
                        st.subheader("📊 Query Results (Live Updates)")
                        st.info(f"📋 Completed {len(all_results)}/{len(queries)} queries - {len(combined_df)} total rows")
                        st.dataframe(combined_df, use_container_width=True)

        # Final success message
        progress_placeholder.success(f"✅ All {len(queries)} queries executed successfully!")

    except Exception as e:
        progress_placeholder.error(f"❌ Error: {str(e)}")
    finally:
        connection.close()

def main():
    st.title("📊 Oracle KPI Dashboard")
    st.markdown("---")
    
    # Load configuration and SQL queries
    config = load_config()
    queries = parse_sql_file()
    
    # Sidebar for inputs
    st.sidebar.header("Query Parameters")
    
    # Date selectors
    col1, col2 = st.sidebar.columns(2)
    with col1:
        from_date = st.date_input(
            "From Date",
            value=date.today().replace(day=1),  # Default to first day of current month
            key="from_date"
        )
    with col2:
        to_date = st.date_input(
            "To Date",
            value=date.today(),
            key="to_date"
        )
    
    # Validate date range
    if from_date > to_date:
        st.sidebar.error("From date must be before or equal to To date")
        return
    
    # Query selection with ALL option
    query_options = ["ALL - Run All Queries"] + [f"{i+1}. {q['description']}" for i, q in enumerate(queries)]
    selected_option = st.sidebar.selectbox(
        "Select KPI Query",
        range(len(query_options)),
        format_func=lambda x: query_options[x]
    )

    # Determine if running all queries or single query
    run_all_queries = (selected_option == 0)
    selected_query_idx = selected_option - 1 if not run_all_queries else None
    
    # Display selected query info
    if run_all_queries:
        st.subheader("Selected: Run All KPI Queries")
        st.info(f"Will execute all {len(queries)} queries sequentially")
    else:
        st.subheader(f"Selected Query: {queries[selected_query_idx]['description']}")

        # Show the query that will be executed for single query
        st.subheader("Query Preview")
        preview_sql = queries[selected_query_idx]['sql'].replace('<from_date>', from_date.strftime('%Y-%m-%d'))
        preview_sql = preview_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))

        with st.expander("📋 View SQL Query", expanded=False):
            st.code(preview_sql, language="sql")

    # Show date range
    st.info(f"Date Range: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")

    # Run query button
    if st.button("🚀 Run Query", type="primary"):
        if run_all_queries:
            run_all_queries_function(queries, from_date, to_date, config)
        else:
            run_single_query_function(queries[selected_query_idx], from_date, to_date, config)

if __name__ == "__main__":
    main()
