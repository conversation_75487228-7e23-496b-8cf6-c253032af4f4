I want to create a streamlit application that lets me choose from_date and to_date from a date selector. 
it should overwrite <from_date> and <to_date> in the sql queries from file Oracle KPIs.sql and run them connecting to oracle database
UI should display the query running and after it completes, display the result in a table. query description is in the comments above the query in the file Oracle KPIs.sql
database credentials should be read from a config file in the same folder


{
    "database": {
        "host": "pac4dtrddl10v.corp.cvscaremark.com",
        "port": 1592,
        "service_name": "TRIDEV1",
        "protocol": "TCPS",
        "username": "TRIORCL",
        "password": "Cntrl#4Dev"
    }
}


python -m streamlit run streamlit_app.py
