import streamlit as st
import pyodbc
import pandas as pd
import json
import re
from datetime import datetime, date
import os
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# Page configuration
st.set_page_config(
    page_title="UDB DB2 KPI Dashboard",
    page_icon="🗄️",
    layout="wide"
)

@st.cache_data
def load_config():
    """Load database configuration from udb_config.json"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        st.error("Config file 'udb_config.json' not found. Please create it with your database credentials.")
        st.stop()
    except json.JSONDecodeError:
        st.error("Invalid JSON in udb_config.json file.")
        st.stop()

@st.cache_data
def parse_sql_file():
    """Parse the UDB KPIs.sql file to extract queries and descriptions"""
    try:
        with open('UDB KPIs.sql', 'r') as f:
            content = f.read()
        
        # Split content into blocks separated by empty lines
        blocks = re.split(r'\n\s*\n', content.strip())
        queries = []
        
        for block in blocks:
            if not block.strip():
                continue
                
            lines = block.strip().split('\n')
            description = ""
            sql_lines = []
            
            # Extract description from first comment line
            for line in lines:
                line = line.strip()
                if line.startswith('--') and not description:
                    description = line[2:].strip()
                elif not line.startswith('--') and line:
                    sql_lines.append(line)
            
            if sql_lines:
                sql = '\n'.join(sql_lines)
                if sql.strip():
                    queries.append({
                        'description': description if description else f"Query {len(queries) + 1}",
                        'sql': sql
                    })
        
        return queries
    except FileNotFoundError:
        st.error("SQL file 'UDB KPIs.sql' not found.")
        st.stop()

def create_connection(db_config):
    """Create DB2 database connection using ODBC"""
    try:
        # DB2 ODBC connection string
        conn_str = f"DRIVER={{IBM DB2 ODBC DRIVER}};DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};"
        connection = pyodbc.connect(conn_str)
        return connection
    except Exception as e:
        st.error(f"Database connection error for {db_config.get('name', 'Unknown')}: {str(e)}")
        return None

def execute_query(connection, sql, from_date, to_date):
    """Execute SQL query with date parameters"""
    try:
        # Replace date placeholders
        formatted_sql = sql.replace('<from_date>', from_date.strftime('%Y-%m-%d'))
        formatted_sql = formatted_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))

        # Execute query using pandas for easier handling
        df = pd.read_sql(formatted_sql, connection)
        return df, formatted_sql
    except Exception as e:
        st.error(f"Query execution error: {str(e)}")
        return None, None

def execute_query_on_database(db_key, db_config, query, from_date, to_date, query_idx):
    """Execute a single query on a specific database (for parallel execution)"""
    try:
        connection = create_connection(db_config)
        if connection is None:
            return {
                'db_key': db_key,
                'query_idx': query_idx,
                'success': False,
                'error': f"Failed to connect to {db_config['name']}",
                'result': None
            }
        
        try:
            result_df, formatted_sql = execute_query(connection, query['sql'], from_date, to_date)
            
            if result_df is not None:
                # Add database identifier and query description
                result_df.insert(0, 'Database', db_config['name'])
                result_df.insert(1, 'KPI_Description', query['description'])
                
                return {
                    'db_key': db_key,
                    'query_idx': query_idx,
                    'success': True,
                    'result': result_df,
                    'formatted_sql': formatted_sql
                }
            else:
                return {
                    'db_key': db_key,
                    'query_idx': query_idx,
                    'success': False,
                    'error': 'Query execution failed',
                    'result': None
                }
        finally:
            connection.close()
            
    except Exception as e:
        return {
            'db_key': db_key,
            'query_idx': query_idx,
            'success': False,
            'error': str(e),
            'result': None
        }

def run_single_query_function(query, from_date, to_date, config):
    """Execute a single query on both databases in parallel"""
    st.subheader("🔄 Executing Query on Both Databases")
    preview_sql = query['sql'].replace('<from_date>', from_date.strftime('%Y-%m-%d'))
    preview_sql = preview_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))
    
    st.info("**Running the following SQL query on both databases:**")
    st.code(preview_sql, language="sql")
    
    # Create progress placeholders
    progress_placeholder = st.empty()
    results_placeholder = st.empty()
    
    progress_placeholder.info("🔌 Connecting to both databases and executing query in parallel...")
    
    # Execute query on both databases in parallel
    with ThreadPoolExecutor(max_workers=2) as executor:
        futures = []
        
        # Submit tasks for both databases
        for db_key in ['database1', 'database2']:
            future = executor.submit(
                execute_query_on_database,
                db_key, config[db_key], query, from_date, to_date, 0
            )
            futures.append(future)
        
        # Collect results as they complete
        results = []
        completed_count = 0
        
        for future in as_completed(futures):
            result = future.result()
            results.append(result)
            completed_count += 1
            
            progress_placeholder.info(f"⚡ Completed {completed_count}/2 databases")
            
            # Update results display
            successful_results = [r for r in results if r['success'] and r['result'] is not None]
            if successful_results:
                combined_df = pd.concat([r['result'] for r in successful_results], ignore_index=True)
                with results_placeholder.container():
                    st.subheader("📊 Query Results (Live Updates)")
                    st.info(f"📋 Completed {len(successful_results)}/2 databases - {len(combined_df)} total rows")
                    st.dataframe(combined_df, use_container_width=True)
    
    # Final status
    successful_results = [r for r in results if r['success']]
    failed_results = [r for r in results if not r['success']]
    
    if len(successful_results) == 2:
        progress_placeholder.success("✅ Query executed successfully on both databases!")
    elif len(successful_results) == 1:
        progress_placeholder.warning(f"⚠️ Query executed on 1/2 databases. Failed: {failed_results[0]['error']}")
    else:
        progress_placeholder.error("❌ Query failed on both databases!")
        for result in failed_results:
            st.error(f"Database {result['db_key']}: {result['error']}")

def run_all_queries_function(queries, from_date, to_date, config):
    """Execute all queries on both databases in parallel"""
    st.subheader("🔄 Executing All Queries on Both Databases")

    # Create placeholders for progress and results
    progress_placeholder = st.empty()
    results_placeholder_db1 = st.empty()
    results_placeholder_db2 = st.empty()

    # Initialize results storage
    db1_results = []
    db2_results = []
    total_tasks = len(queries) * 2  # queries * databases

    progress_placeholder.info("🔌 Connecting to both databases and executing all queries in parallel...")

    # Execute all queries on both databases in parallel
    with ThreadPoolExecutor(max_workers=4) as executor:  # Allow up to 4 concurrent connections
        futures = []

        # Submit all tasks (each query on each database)
        for query_idx, query in enumerate(queries):
            for db_key in ['database1', 'database2']:
                future = executor.submit(
                    execute_query_on_database,
                    db_key, config[db_key], query, from_date, to_date, query_idx
                )
                futures.append(future)

        # Collect results as they complete
        completed_count = 0

        for future in as_completed(futures):
            result = future.result()
            completed_count += 1

            # Update progress
            progress_placeholder.info(f"⚡ Completed {completed_count}/{total_tasks} query executions")

            # Sort results by database
            if result['success'] and result['result'] is not None:
                if result['db_key'] == 'database1':
                    db1_results.append(result)
                else:
                    db2_results.append(result)

            # Update results displays for both databases
            if db1_results:
                db1_combined = pd.concat([r['result'] for r in db1_results], ignore_index=True)
                with results_placeholder_db1.container():
                    st.subheader(f"📊 {config['database1']['name']} Results")
                    st.info(f"📋 Completed {len(db1_results)}/{len(queries)} queries - {len(db1_combined)} total rows")
                    st.dataframe(db1_combined, use_container_width=True)

            if db2_results:
                db2_combined = pd.concat([r['result'] for r in db2_results], ignore_index=True)
                with results_placeholder_db2.container():
                    st.subheader(f"📊 {config['database2']['name']} Results")
                    st.info(f"📋 Completed {len(db2_results)}/{len(queries)} queries - {len(db2_combined)} total rows")
                    st.dataframe(db2_combined, use_container_width=True)

    # Final status
    total_successful = len(db1_results) + len(db2_results)
    progress_placeholder.success(f"✅ Completed {total_successful}/{total_tasks} query executions successfully!")

def main():
    st.title("🗄️ UDB DB2 KPI Dashboard")
    st.markdown("---")

    # Load configuration and SQL queries
    config = load_config()
    queries = parse_sql_file()

    # Sidebar for inputs
    st.sidebar.header("Query Parameters")

    # Date selectors
    col1, col2 = st.sidebar.columns(2)
    with col1:
        from_date = st.date_input(
            "From Date",
            value=date.today().replace(day=1),  # Default to first day of current month
            key="from_date"
        )
    with col2:
        to_date = st.date_input(
            "To Date",
            value=date.today(),
            key="to_date"
        )

    # Validate date range
    if from_date > to_date:
        st.sidebar.error("From date must be before or equal to To date")
        return

    # Query selection with ALL option
    query_options = ["ALL - Run All Queries"] + [f"{i+1}. {q['description']}" for i, q in enumerate(queries)]
    selected_option = st.sidebar.selectbox(
        "Select KPI Query",
        range(len(query_options)),
        format_func=lambda x: query_options[x]
    )

    # Determine if running all queries or single query
    run_all_queries = (selected_option == 0)
    selected_query_idx = selected_option - 1 if not run_all_queries else None

    # Display database information
    st.subheader("🗄️ Database Configuration")
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**Database 1:** {config['database1']['name']}\n\n**Host:** {config['database1']['host']}")
    with col2:
        st.info(f"**Database 2:** {config['database2']['name']}\n\n**Host:** {config['database2']['host']}")

    # Display selected query info
    if run_all_queries:
        st.subheader("Selected: Run All KPI Queries on Both Databases")
        st.info(f"Will execute all {len(queries)} queries in parallel on both databases")
    else:
        st.subheader(f"Selected Query: {queries[selected_query_idx]['description']}")

        # Show the query that will be executed for single query
        st.subheader("Query Preview")
        preview_sql = queries[selected_query_idx]['sql'].replace('<from_date>', from_date.strftime('%Y-%m-%d'))
        preview_sql = preview_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))

        with st.expander("📋 View SQL Query", expanded=False):
            st.code(preview_sql, language="sql")

    # Show date range
    st.info(f"Date Range: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")

    # Run query button
    if st.button("🚀 Run Query", type="primary"):
        if run_all_queries:
            run_all_queries_function(queries, from_date, to_date, config)
        else:
            run_single_query_function(queries[selected_query_idx], from_date, to_date, config)

if __name__ == "__main__":
    main()
