#!/usr/bin/env python3
"""
Startup script for UDB DB2 KPI Dashboard
Allows running on a custom port to avoid conflicts with other Streamlit apps
"""

import subprocess
import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description='Run UDB DB2 KPI Dashboard')
    parser.add_argument('--port', type=int, default=8502, 
                       help='Port to run the application on (default: 8502)')
    parser.add_argument('--host', type=str, default='localhost',
                       help='Host to bind to (default: localhost)')
    
    args = parser.parse_args()
    
    print(f"Starting UDB DB2 KPI Dashboard on {args.host}:{args.port}")
    print("Press Ctrl+C to stop the application")
    
    # Run streamlit with custom port
    cmd = [
        sys.executable, '-m', 'streamlit', 'run', 'udb.py',
        '--server.port', str(args.port),
        '--server.address', args.host,
        '--server.headless', 'false'
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"Error running application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
