@echo off
REM Batch script to run UDB application on port 8502
echo ===============================================
echo    UDB DB2 KPI Dashboard
echo ===============================================
echo.
echo Starting application on port 8502...
echo URL: http://localhost:8502
echo.
echo Press Ctrl+C to stop the application
echo.
streamlit run udb.py --server.port 8502 --server.address localhost --server.headless false
echo.
echo Application stopped.
pause
