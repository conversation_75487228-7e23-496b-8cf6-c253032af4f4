-- orders sent to PPS
select PPS_TOTAL, TOTAL, round((PPS_TOTAL/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct hs.BRT_ID) as PPS_TOTAL
  from TPAU_PAS_STATUS_HS hs, TODR_PAS_ORDER ord
  where hs.RQT_CD = 1
    and hs.SRE_CD = 2
    and hs.SSY_CD = 1
    and ord.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and ord.BRT_ID = hs.BRT_ID
    and ord.ODR_RTP_IN = 0
) a1,
(
  select count(*) as TOTAL
  from TODR_PAS_ORDER
  where ODR_RTP_IN = 0
    and HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
) a2
with ur

-- orders sent to Scandata
select SDS_TOTAL, TOTAL, round((SDS_TOTAL/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct hs.BRT_ID) as SDS_TOTAL
  from TPAU_PAS_STATUS_HS hs, TODR_PAS_ORDER ord
  where hs.RQT_CD = 1
    and hs.SRE_CD = 2
    and hs.SSY_CD = 2
    and ord.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and ord.BRT_ID = hs.BRT_ID
    and ord.ODR_RTP_IN = 0
) a1,
(
  select count(*) as TOTAL
  from TODR_PAS_ORDER
  where ODR_RTP_IN = 0
    and HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
) a2
with ur

-- LINKS orders sent to Billing while also being marked as Scandata Shipped and PPS Filled in Tristar
select SENT, TOTAL, round((SENT/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct log1.BRT_ID) as TOTAL
  from TCMG_PAS_CMMUN_LOG log1
  where log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and log1.RQT_CD = 10
    and exists
    (
      select 1
      from TCMG_PAS_CMMUN_LOG log2
      where log2.BRT_ID = log1.BRT_ID
        and log2.RQT_CD = 13
    )
) a1,
(
  select count(distinct log1.BRT_ID) as SENT
  from TCMG_PAS_CMMUN_LOG log1, TPAU_PAS_STATUS_HS hs
  where log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and log1.RQT_CD = 10
    and exists
    (
      select 1
      from TCMG_PAS_CMMUN_LOG log2
      where log2.BRT_ID = log1.BRT_ID
        and log2.RQT_CD = 13
    )
    and log1.BRT_ID = hs.BRT_ID
    and hs.RQT_CD = 1
    and hs.SSY_CD = 4
    and hs.SRE_CD = 2
) a2
with ur

-- LINKS orders sent to SAP while also being marked as Scandata Shipped and PPS Filled in Tristar
select SENT, TOTAL, round((SENT/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct log1.BRT_ID) as TOTAL
  from TCMG_PAS_CMMUN_LOG log1
  where log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and log1.RQT_CD = 10
    and exists
    (
      select 1
      from TCMG_PAS_CMMUN_LOG log2
      where log2.BRT_ID = log1.BRT_ID
        and log2.RQT_CD = 13
    )
) a1,
(
  select count(distinct log1.BRT_ID) as SENT
  from TCMG_PAS_CMMUN_LOG log1, TPAU_PAS_STATUS_HS hs
  where log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and log1.RQT_CD = 10
    and exists
    (
      select 1
      from TCMG_PAS_CMMUN_LOG log2
      where log2.BRT_ID = log1.BRT_ID
        and log2.RQT_CD = 13
    )
    and log1.BRT_ID = hs.BRT_ID
    and hs.RQT_CD = 1
    and hs.SSY_CD = 5
    and hs.SRE_CD = 2
) a2
with ur

-- LINKS orders in Tristar marked Scandata Shipped > 7 days
select NOT_SHIPPED, DOWNLOADED, round((NOT_SHIPPED/(DOWNLOADED*1.0))*100, 2) as PERCENT
from
(
  select count(*) as DOWNLOADED
  from TCMG_PAS_CMMUN_LOG log1, TODR_PAS_ORDER ord
  where log1.RQT_CD = 1
    and log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and log1.BRT_ID = ord.BRT_ID
    and ord.ODR_RTP_IN = 0
    and ord.SND_SRE_CD not in (2,10)
    and ord.P2S_SRE_CD not in (2,10)
) a1,
(
  select count(*) as NOT_SHIPPED
  from TCMG_PAS_CMMUN_LOG log1, TODR_PAS_ORDER ord
  where log1.RQT_CD = 1
    and log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and exists
    (
      select 1
      from TCMG_PAS_CMMUN_LOG log2
      where log2.BRT_ID = log1.BRT_ID
        and log2.RQT_CD = 13
        and log2.HSC_TS < log1.HSC_TS + 7 day
    )
    and log1.BRT_ID = ord.BRT_ID
    and ord.ODR_RTP_IN = 0
    and ord.SND_SRE_CD not in (2,10)
    and ord.P2S_SRE_CD not in (2,10)
) a2
with ur

-- LINKS orders in Tristar marked PPS Filled > 7 day
select NOT_FILLED, DOWNLOADED, round((NOT_FILLED/(DOWNLOADED*1.0))*100, 2) as PERCENT
from
(
  select count(*) as DOWNLOADED
  from TCMG_PAS_CMMUN_LOG log1, TODR_PAS_ORDER ord
  where log1.RQT_CD = 1
    and log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and log1.BRT_ID = ord.BRT_ID
    and ord.ODR_RTP_IN = 0
    and ord.SND_SRE_CD not in (2,10)
    and ord.P2S_SRE_CD not in (2,10)
) a1,
(
  select count(*) as NOT_FILLED
  from TCMG_PAS_CMMUN_LOG log1, TODR_PAS_ORDER ord
  where log1.RQT_CD = 1
    and log1.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and exists
    (
      select 1
      from TCMG_PAS_CMMUN_LOG log2
      where log2.BRT_ID = log1.BRT_ID
        and log2.RQT_CD = 10
        and log2.HSC_TS < log1.HSC_TS + 7 day
    )
    and log1.BRT_ID = ord.BRT_ID
    and ord.ODR_RTP_IN = 0
    and ord.SND_SRE_CD not in (2,10)
    and ord.P2S_SRE_CD not in (2,10)
) a2
with ur;

-- RTP orders sent to Billing
select BILLING_TOTAL, TOTAL, round((BILLING_TOTAL/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct hs.BRT_ID) as BILLING_TOTAL
  from TPAU_PAS_STATUS_HS hs, TODR_PAS_ORDER ord
  where hs.RQT_CD = 1
    and hs.SRE_CD = 2
    and hs.SSY_CD = 4
    and ord.HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
    and ord.BRT_ID = hs.BRT_ID
    and ord.ODR_RTP_IN = 1
) a1,
(
  select count(*) as TOTAL
  from TODR_PAS_ORDER
  where ODR_RTP_IN = 1
    and HSC_TS between TO_TIMESTAMP('<from_date> 00:00:00', 'YYYY-MM-DD HH24:MI:SS') and TO_TIMESTAMP('<to_date> 23:59:59', 'YYYY-MM-DD HH24:MI:SS')
) a2
with ur;