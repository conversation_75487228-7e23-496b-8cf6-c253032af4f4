# LINKS scandata send p95
index="tristar" host=prdtrs* perfId="scandata-order-send" | stats perc95(timeElapsed)

# LINKS scandata order list fetch p95
index="tristar" host=prdtrs* perfId="scandata-order-fetch" | stats perc95(timeElapsed)

# LINKS scandata order uploaded p95
index="tristar" host=prdtrs* perfId="scandata-order-upload" | stats perc95(timeElapsed)

# DaaS order intake new order p95
index="cloudeng_aks" cluster_name="aks-pbm-tristar-prod-usc" name="DaaS Order Intake Service" perfId="intake-request-complete" | stats perc95(timeElapsed)

# DaaS scandata send p95
index="cloudeng_aks" cluster_name="aks-pbm-tristar-prod-usc" name="Backend Dispatcher Service" perfId="scandata-send" | stats perc95(timeElapsed)

# DaaS inbound dispensing events p95
index="cloudeng_aks" cluster_name="aks-pbm-tristar-prod-usc" name="Backend Receiver Service" perfId="event-request-processed" | stats perc95(timeElapsed)

# DaaS outbound dispensing events p95
index="cloudeng_aks" cluster_name="aks-pbm-tristar-prod-usc" name="Dash Responder Service" perfId="processing-total" | stats perc95(timeElapsed)

# DaaS return package p95
index="cloudeng_aks" cluster_name="aks-pbm-tristar-prod-usc" name="Return Package Service" perfId="return-package" | stats perc95(timeElapsed)