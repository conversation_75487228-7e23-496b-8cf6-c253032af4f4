#!/usr/bin/env python3
"""
Test script to verify DB2 database connections
Run this before using the main UDB application
"""

import pyodbc
import json
import sys

def load_config():
    """Load database configuration"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("ERROR: Config file 'udb_config.json' not found.")
        return None
    except json.JSONDecodeError:
        print("ERROR: Invalid JSON in udb_config.json file.")
        return None

def list_odbc_drivers():
    """List all available ODBC drivers"""
    try:
        drivers = pyodbc.drivers()
        print("Available ODBC drivers:")
        for i, driver in enumerate(drivers, 1):
            print(f"  {i}. {driver}")
        return drivers
    except Exception as e:
        print(f"Error listing ODBC drivers: {e}")
        return []

def test_connection(db_config, db_name):
    """Test connection to a single database"""
    print(f"\n--- Testing connection to {db_name} ({db_config['name']}) ---")
    print(f"Host: {db_config['host']}")
    print(f"Port: {db_config['port']}")
    print(f"Database: {db_config['database']}")
    print(f"Username: {db_config['username']}")
    
    # List of possible DB2 ODBC driver names to try
    driver_names = [
        "IBM DB2 ODBC DRIVER",
        "IBM DATA SERVER DRIVER for ODBC",
        "IBM DB2 ODBC DRIVER - DB2COPY1",
        "IBM ODBC DB2 DRIVER",
        "DB2"
    ]
    
    for driver_name in driver_names:
        try:
            print(f"\nTrying driver: {driver_name}")
            conn_str = f"DRIVER={{{driver_name}}};DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};"
            
            connection = pyodbc.connect(conn_str, timeout=30)
            print(f"✅ SUCCESS: Connected using driver '{driver_name}'")
            
            # Test a simple query
            cursor = connection.cursor()
            cursor.execute("SELECT CURRENT TIMESTAMP FROM SYSIBM.SYSDUMMY1")
            result = cursor.fetchone()
            print(f"✅ Test query successful: {result[0]}")
            
            cursor.close()
            connection.close()
            return True
            
        except pyodbc.Error as e:
            print(f"❌ FAILED with driver '{driver_name}': {e}")
            continue
        except Exception as e:
            print(f"❌ FAILED with driver '{driver_name}': {e}")
            continue
    
    # Try alternative connection string format
    try:
        print(f"\nTrying alternative connection format...")
        conn_str = f"DSN=;DRIVER={{IBM DB2 ODBC DRIVER}};Database={db_config['database']};Hostname={db_config['host']};Port={db_config['port']};Protocol=TCPIP;Uid={db_config['username']};Pwd={db_config['password']};"
        
        connection = pyodbc.connect(conn_str, timeout=30)
        print(f"✅ SUCCESS: Connected using alternative format")
        
        # Test a simple query
        cursor = connection.cursor()
        cursor.execute("SELECT CURRENT TIMESTAMP FROM SYSIBM.SYSDUMMY1")
        result = cursor.fetchone()
        print(f"✅ Test query successful: {result[0]}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ FAILED with alternative format: {e}")
    
    print(f"❌ All connection attempts failed for {db_name}")
    return False

def main():
    print("=== DB2 Connection Test ===")
    
    # List available ODBC drivers
    drivers = list_odbc_drivers()
    
    if not drivers:
        print("\n❌ No ODBC drivers found. Please install IBM DB2 ODBC driver.")
        sys.exit(1)
    
    # Check for DB2 drivers
    db2_drivers = [d for d in drivers if 'DB2' in d.upper() or 'IBM' in d.upper()]
    if not db2_drivers:
        print("\n⚠️  No DB2 ODBC drivers found. Available drivers:")
        for driver in drivers:
            print(f"    - {driver}")
        print("\nPlease install IBM DB2 ODBC driver or IBM Data Server Driver.")
    else:
        print(f"\n✅ Found {len(db2_drivers)} DB2-related driver(s):")
        for driver in db2_drivers:
            print(f"    - {driver}")
    
    # Load configuration
    config = load_config()
    if not config:
        sys.exit(1)
    
    # Test connections
    success_count = 0
    total_count = 0
    
    for db_key, db_config in config.items():
        total_count += 1
        if test_connection(db_config, db_key):
            success_count += 1
    
    print(f"\n=== Summary ===")
    print(f"Successful connections: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("✅ All database connections successful! You can now run the UDB application.")
    elif success_count > 0:
        print("⚠️  Some database connections failed. Check the error messages above.")
    else:
        print("❌ All database connections failed. Please check your configuration and ODBC driver installation.")
        sys.exit(1)

if __name__ == "__main__":
    main()
