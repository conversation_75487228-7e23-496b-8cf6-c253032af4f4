#!/usr/bin/env python3
"""
Test SSL DB2 connections based on your JDBC URL format
*******************************************************************************************;
"""

import pyodbc
import json
import sys

def load_config():
    """Load database configuration"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("ERROR: Config file 'udb_config.json' not found.")
        return None

def test_ssl_connections(db_config):
    """Test SSL-enabled DB2 connections"""
    print(f"\n=== Testing SSL Connections for {db_config['name']} ===")
    print(f"Based on JDBC URL: jdbc:db2://{db_config['host']}:{db_config['port']}/{db_config['database']}:sslConnection=true")
    
    # Get available drivers
    try:
        drivers = pyodbc.drivers()
        db2_drivers = [d for d in drivers if 'DB2' in d.upper()]
        print(f"Available DB2 drivers: {db2_drivers}")
    except Exception as e:
        print(f"Error getting drivers: {e}")
        return False
    
    if not db2_drivers:
        print("No DB2 drivers found!")
        return False
    
    # SSL connection string formats based on your JDBC URL
    ssl_formats = [
        # Format 1: SECURITY=SSL
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};SECURITY=SSL;",
        
        # Format 2: SSLCONNECTION=true (direct translation from JDBC)
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};SSLCONNECTION=true;",
        
        # Format 3: SSL=true
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};SSL=true;",
        
        # Format 4: With both SSL and message retrieval parameter
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};SECURITY=SSL;RETRIEVEMESSAGESFROMSERVERONGETMESSAGE=true;",
        
        # Format 5: SSL with certificate validation disabled
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};SECURITY=SSL;SSLCERTVALIDATION=false;",
        
        # Format 6: ENCRYPTION=SSL
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};ENCRYPTION=SSL;",
        
        # Format 7: With CONNECTTYPE and SSL
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};CONNECTTYPE=1;SECURITY=SSL;",
        
        # Format 8: Direct JDBC parameter translation
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};SSLCONNECTION=true;RETRIEVEMESSAGESFROMSERVERONGETMESSAGE=true;"
    ]
    
    for driver in db2_drivers[:2]:  # Test first 2 drivers
        print(f"\n--- Testing SSL with driver: {driver} ---")
        
        for i, format_template in enumerate(ssl_formats, 1):
            try:
                # Build connection string
                conn_str = format_template.format(
                    driver=driver,
                    database=db_config['database'],
                    host=db_config['host'],
                    port=db_config['port'],
                    username=db_config['username'],
                    password=db_config['password']
                )
                
                # Show the connection string (with password masked)
                safe_conn_str = conn_str.replace(db_config['password'], '***')
                print(f"SSL Format {i}: {safe_conn_str}")
                
                # Try to connect
                print(f"  Connecting with SSL...", end=" ")
                connection = pyodbc.connect(conn_str, timeout=30)
                print("SUCCESS!")
                
                # Test with a simple query
                print(f"  Testing query...", end=" ")
                cursor = connection.cursor()
                cursor.execute("SELECT CURRENT TIMESTAMP FROM SYSIBM.SYSDUMMY1")
                result = cursor.fetchone()
                print(f"SUCCESS! Result: {result[0]}")
                
                cursor.close()
                connection.close()
                
                print(f"\n🎉 SSL CONNECTION WORKING!")
                print(f"Driver: {driver}")
                print(f"SSL Format: {i}")
                print(f"Connection String: {safe_conn_str}")
                print(f"\nThis matches your JDBC URL requirement for SSL!")
                
                return True
                
            except pyodbc.Error as e:
                error_code = e.args[0] if e.args else 'Unknown'
                error_msg = str(e.args[1]) if len(e.args) > 1 else str(e)
                print(f"FAILED: [{error_code}] {error_msg[:100]}...")
                
                # Analyze SSL-specific errors
                if 'SQL30081N' in error_msg:
                    if 'SSL' in error_msg or 'TLS' in error_msg:
                        print(f"    → SSL/TLS configuration issue")
                    else:
                        print(f"    → Communication error (may need different SSL parameters)")
                elif 'SSL' in error_msg:
                    print(f"    → SSL-specific error")
                elif 'certificate' in error_msg.lower():
                    print(f"    → SSL certificate issue")
                    
            except Exception as e:
                print(f"FAILED: {str(e)[:100]}...")
    
    print(f"\n❌ No SSL connections worked for {db_config['name']}")
    
    # Suggest next steps
    print(f"\n💡 SSL Connection Troubleshooting:")
    print(f"1. Your JDBC URL shows sslConnection=true is required")
    print(f"2. The DB2 server expects SSL connections on port {db_config['port']}")
    print(f"3. You may need SSL certificates installed")
    print(f"4. Try connecting with DB2 command line: db2 connect to {db_config['database']} user {db_config['username']}")
    print(f"5. Check if your DB2 client has SSL certificates configured")
    
    return False

def main():
    print("=== SSL DB2 Connection Tester ===")
    print("Testing SSL connections based on your JDBC URL format")
    print("JDBC: **************************************************************************************")
    
    config = load_config()
    if not config:
        sys.exit(1)
    
    success_count = 0
    for db_config in config.values():
        if test_ssl_connections(db_config):
            success_count += 1
    
    print(f"\n=== Final Summary ===")
    if success_count > 0:
        print(f"✅ Found {success_count} working SSL connection(s)!")
        print("Use the successful SSL connection string format in your Streamlit app.")
    else:
        print("❌ No SSL connections worked.")
        print("\nThis confirms that SSL configuration is the issue.")
        print("Your JDBC URL requires SSL, but the ODBC drivers may need:")
        print("- SSL certificates installed")
        print("- Different SSL parameter names")
        print("- DB2 client SSL configuration")

if __name__ == "__main__":
    main()
