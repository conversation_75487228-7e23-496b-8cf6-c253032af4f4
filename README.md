# Oracle KPI Dashboard

A Streamlit application for running Oracle SQL queries with date parameters and displaying KPI results.

## Features

- Interactive date range selection
- Multiple KPI queries with descriptions
- **Run All Queries**: Execute all KPIs sequentially with live table updates
- Real-time query execution with progress indicators
- **Live Query Display**: Shows which query is currently running
- Results displayed in formatted tables with KPI descriptions
- Summary statistics for numeric columns
- View executed SQL queries with syntax highlighting

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Database Connection

Edit the `config.json` file with your Oracle database credentials:

```json
{
    "database": {
        "host": "your_oracle_host",
        "port": 1521,
        "service_name": "your_service_name",
        "username": "your_username",
        "password": "your_password"
    }
}
```

### 3. Oracle Client (Optional)

The application uses the new `oracledb` library which can work in "Thin mode" without requiring Oracle Instant Client installation. However, if you need advanced features, you can optionally install Oracle Instant Client:

- **Windows**: Download from Oracle website and add to PATH
- **Linux**: Install via package manager or download from Oracle
- **macOS**: Install via Homebrew: `brew install oracle-instantclient`

For most use cases, the Thin mode (default) will work perfectly without any additional installation.

### 4. Run the Application

```bash
streamlit run streamlit_app.py
```

The application will open in your default web browser at `http://localhost:8501`

## Usage

1. **Select Date Range**: Use the sidebar date pickers to choose from_date and to_date
2. **Choose KPI Query**: Select from the dropdown list of available queries:
   - **ALL - Run All Queries**: Executes all KPI queries sequentially with live updates
   - **Individual Queries**: Run a specific KPI query
3. **Run Query**: Click the "Run Query" button to execute
4. **View Results**:
   - **Single Query**: Results displayed in a table with summary statistics
   - **All Queries**: Combined results table with KPI descriptions as the first column, updated live as queries complete

## Available KPI Queries

1. **RTP printpacks merged** - Percentage of merged printpacks
2. **New orders sent to PPS and Scandata** - Processing percentage
3. **DaaS orders marked Scandata Metered sent to SAP Inventory Decrement** - Sent percentage
4. **PPS completed within 7 days (6a)** - Completion percentage
5. **Scandata completed within 7 days (6b,6co)** - Completion percentage
6. **p95 of fetching printpacks from carelon** - Performance metric

## Files

- `streamlit_app.py` - Main Streamlit application
- `Oracle KPIs.sql` - SQL queries with date placeholders
- `config.json` - Database configuration
- `requirements.txt` - Python dependencies

## Troubleshooting

- **Database Connection Issues**: Verify your config.json credentials and network connectivity
- **Installation Issues**: The new `oracledb` library should install without compilation issues
- **Query Errors**: Check the executed SQL in the expandable section for syntax issues
- **Performance**: For better performance with large datasets, consider installing Oracle Instant Client to use "Thick mode"
