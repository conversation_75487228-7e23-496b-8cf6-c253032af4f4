# Oracle KPI Dashboard

A Streamlit application for running Oracle SQL queries with date parameters and displaying KPI results.

## Features

- Interactive date range selection
- Multiple KPI queries with descriptions
- Real-time query execution with progress indicators
- Results displayed in formatted tables
- Summary statistics for numeric columns
- View executed SQL queries

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Configure Database Connection

Edit the `config.json` file with your Oracle database credentials:

```json
{
    "database": {
        "host": "your_oracle_host",
        "port": 1521,
        "service_name": "your_service_name",
        "username": "your_username",
        "password": "your_password"
    }
}
```

### 3. Oracle Client Installation

Make sure you have Oracle Instant Client installed on your system:

- **Windows**: Download from Oracle website and add to PATH
- **Linux**: Install via package manager or download from Oracle
- **macOS**: Install via Homebrew: `brew install oracle-instantclient`

### 4. Run the Application

```bash
streamlit run streamlit_app.py
```

The application will open in your default web browser at `http://localhost:8501`

## Usage

1. **Select Date Range**: Use the sidebar date pickers to choose from_date and to_date
2. **Choose <PERSON>PI Query**: Select from the dropdown list of available queries
3. **Run Query**: Click the "Run Query" button to execute
4. **View Results**: Results will be displayed in a table format with summary statistics

## Available KPI Queries

1. **RTP printpacks merged** - Percentage of merged printpacks
2. **New orders sent to PPS and Scandata** - Processing percentage
3. **DaaS orders marked Scandata Metered sent to SAP Inventory Decrement** - Sent percentage
4. **PPS completed within 7 days (6a)** - Completion percentage
5. **Scandata completed within 7 days (6b,6co)** - Completion percentage
6. **p95 of fetching printpacks from carelon** - Performance metric

## Files

- `streamlit_app.py` - Main Streamlit application
- `Oracle KPIs.sql` - SQL queries with date placeholders
- `config.json` - Database configuration
- `requirements.txt` - Python dependencies

## Troubleshooting

- **Database Connection Issues**: Verify your config.json credentials and network connectivity
- **Oracle Client Issues**: Ensure Oracle Instant Client is properly installed and configured
- **Query Errors**: Check the executed SQL in the expandable section for syntax issues
