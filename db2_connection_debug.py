#!/usr/bin/env python3
"""
Advanced DB2 connection debugging tool
Tests multiple connection methods and provides detailed diagnostics
"""

import pyodbc
import json
import sys
import socket

def load_config():
    """Load database configuration"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("ERROR: Config file 'udb_config.json' not found.")
        return None

def test_basic_connectivity(host, port):
    """Test basic network connectivity"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def get_available_drivers():
    """Get all available ODBC drivers"""
    try:
        return pyodbc.drivers()
    except:
        return []

def test_connection_methods(db_config):
    """Test multiple connection methods for DB2"""
    print(f"\n=== Testing {db_config['name']} ===")
    print(f"Host: {db_config['host']}")
    print(f"Port: {db_config['port']}")
    print(f"Database: {db_config['database']}")
    print(f"Username: {db_config['username']}")
    
    # Test network connectivity first
    print(f"\n1. Testing network connectivity...")
    if test_basic_connectivity(db_config['host'], db_config['port']):
        print("✅ Network connectivity OK")
    else:
        print("❌ Network connectivity FAILED")
        return False
    
    # Get available drivers
    drivers = get_available_drivers()
    db2_drivers = [d for d in drivers if 'DB2' in d.upper() or 'IBM' in d.upper()]
    
    print(f"\n2. Available DB2 drivers: {db2_drivers}")
    
    # Connection string variations to try
    connection_strings = []
    
    # Method 1: Standard IBM DB2 ODBC format
    for driver in db2_drivers:
        connection_strings.append({
            'method': f'Standard ODBC with {driver}',
            'conn_str': f"DRIVER={{{driver}}};DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};"
        })
    
    # Method 2: Alternative format
    for driver in db2_drivers:
        connection_strings.append({
            'method': f'Alternative format with {driver}',
            'conn_str': f"DRIVER={{{driver}}};Database={db_config['database']};Hostname={db_config['host']};Port={db_config['port']};Protocol=TCPIP;Uid={db_config['username']};Pwd={db_config['password']};"
        })
    
    # Method 3: Simplified format
    for driver in db2_drivers:
        connection_strings.append({
            'method': f'Simplified format with {driver}',
            'conn_str': f"DRIVER={{{driver}}};SERVER={db_config['host']};PORT={db_config['port']};DATABASE={db_config['database']};UID={db_config['username']};PWD={db_config['password']};"
        })
    
    # Method 4: DSN-less with explicit parameters
    for driver in db2_drivers:
        connection_strings.append({
            'method': f'DSN-less explicit with {driver}',
            'conn_str': f"DRIVER={{{driver}}};DBALIAS={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};CONNECTTYPE=1;AUTHENTICATION=SERVER;"
        })
    
    print(f"\n3. Testing {len(connection_strings)} connection methods...")
    
    for i, conn_info in enumerate(connection_strings, 1):
        print(f"\n   Method {i}: {conn_info['method']}")
        print(f"   Connection string: {conn_info['conn_str'][:100]}...")
        
        try:
            conn = pyodbc.connect(conn_info['conn_str'], timeout=30)
            print("   ✅ CONNECTION SUCCESSFUL!")
            
            # Test a simple query
            try:
                cursor = conn.cursor()
                cursor.execute("SELECT CURRENT TIMESTAMP FROM SYSIBM.SYSDUMMY1")
                result = cursor.fetchone()
                print(f"   ✅ Test query successful: {result[0]}")
                cursor.close()
            except Exception as e:
                print(f"   ⚠️  Connection OK but query failed: {e}")
            
            conn.close()
            return True
            
        except pyodbc.Error as e:
            error_code = e.args[0] if e.args else 'Unknown'
            error_msg = e.args[1] if len(e.args) > 1 else str(e)
            print(f"   ❌ FAILED: [{error_code}] {error_msg}")
            
            # Provide specific guidance based on error codes
            if 'SQL30081N' in error_msg:
                print("   💡 This is a communication error. Possible causes:")
                print("      - DB2 server is not running")
                print("      - Port is blocked by firewall")
                print("      - Network routing issues")
                print("      - SSL/TLS configuration mismatch")
            elif 'SQL30082N' in error_msg:
                print("   💡 This is a security/authentication error. Check:")
                print("      - Username and password")
                print("      - User permissions on the database")
            elif 'SQL1013N' in error_msg:
                print("   💡 Database not found. Check:")
                print("      - Database name spelling")
                print("      - Database is cataloged on the server")
            elif 'IM002' in error_msg:
                print("   💡 Driver not found. Check:")
                print("      - ODBC driver installation")
                print("      - Driver name spelling")
                
        except Exception as e:
            print(f"   ❌ FAILED: {e}")
    
    print(f"\n❌ All connection methods failed for {db_config['name']}")
    return False

def main():
    print("=== Advanced DB2 Connection Diagnostics ===")
    
    # Load configuration
    config = load_config()
    if not config:
        sys.exit(1)
    
    # Show system info
    print(f"\nSystem Information:")
    print(f"Python version: {sys.version}")
    print(f"PyODBC version: {pyodbc.version}")
    
    # Show available drivers
    drivers = get_available_drivers()
    print(f"\nAll available ODBC drivers ({len(drivers)}):")
    for i, driver in enumerate(drivers, 1):
        marker = "🔹" if 'DB2' in driver.upper() or 'IBM' in driver.upper() else "  "
        print(f"  {marker} {i:2d}. {driver}")
    
    # Test each database
    success_count = 0
    for db_key, db_config in config.items():
        if test_connection_methods(db_config):
            success_count += 1
    
    print(f"\n=== Final Summary ===")
    print(f"Successful connections: {success_count}/{len(config)}")
    
    if success_count == 0:
        print("\n🔧 Troubleshooting Steps:")
        print("1. Verify DB2 client libraries are installed")
        print("2. Check if you need to be on corporate VPN")
        print("3. Confirm database server is running")
        print("4. Verify firewall settings allow the connection")
        print("5. Test with DB2 command line tools (db2 connect)")
        print("6. Consider using IBM Data Server Driver instead of ODBC")

if __name__ == "__main__":
    main()
