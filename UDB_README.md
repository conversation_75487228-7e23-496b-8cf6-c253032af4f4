# UDB DB2 KPI Dashboard

A Streamlit application for running UDB DB2 SQL queries with date parameters in parallel across two databases and displaying KPI results.

## Features

- **Parallel Database Execution**: Run queries simultaneously on two different DB2 databases
- Interactive date range selection
- Multiple KPI queries with descriptions
- **Run All Queries**: Execute all KPIs in parallel across both databases
- **Live Results Display**: Separate tables for each database updated in real-time
- Real-time query execution with progress indicators
- Results displayed in formatted tables with KPI descriptions and database identifiers
- Summary statistics for numeric columns
- View executed SQL queries with syntax highlighting

## Setup Instructions

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

Note: The `ibm-db` package requires IBM DB2 client libraries to be installed on your system.

### 2. Configure Database Connections

Edit the `udb_config.json` file with your DB2 database credentials:

```json
{
    "database1": {
        "name": "Database 1 (Production)",
        "host": "your_db2_host_1",
        "port": 50000,
        "database": "your_database_name_1",
        "username": "your_username_1",
        "password": "your_password_1"
    },
    "database2": {
        "name": "Database 2 (Staging)",
        "host": "your_db2_host_2", 
        "port": 50000,
        "database": "your_database_name_2",
        "username": "your_username_2",
        "password": "your_password_2"
    }
}
```

### 3. IBM DB2 Client Installation

Make sure you have IBM DB2 client libraries installed:

- **Windows**: Install IBM Data Server Driver Package
- **Linux**: Install db2 client via package manager or IBM installer
- **macOS**: Install IBM DB2 client libraries

### 4. Run the Application

```bash
streamlit run udb.py
```

The application will open in your default web browser at `http://localhost:8501`

## Usage

1. **Select Date Range**: Use the sidebar date pickers to choose from_date and to_date
2. **Choose KPI Query**: Select from the dropdown list of available queries:
   - **ALL - Run All Queries**: Executes all KPI queries in parallel on both databases
   - **Individual Queries**: Run a specific KPI query on both databases
3. **Run Query**: Click the "Run Query" button to execute
4. **View Results**: 
   - **Single Query**: Results displayed in combined table with database identifiers
   - **All Queries**: Separate tables for each database, updated live as queries complete

## Available KPI Queries

1. **orders sent to PPS** - Orders processing percentage
2. **orders sent to Scandata** - Scandata processing percentage
3. **LINKS orders sent to Billing while also being marked as Scandata Shipped and PPS Filled in Tristar** - Complex billing workflow percentage
4. **LINKS orders sent to SAP while also being marked as Scandata Shipped and PPS Filled in Tristar** - SAP integration percentage
5. **LINKS orders in Tristar marked Scandata Shipped > 7 days** - Delayed shipping percentage
6. **LINKS orders in Tristar marked PPS Filled > 7 day** - Delayed filling percentage
7. **RTP orders sent to Billing** - RTP billing percentage

## Parallel Execution Features

### Single Query Mode
- Executes the selected query on both databases simultaneously
- Displays combined results with database identifiers
- Shows progress as each database completes

### All Queries Mode
- Executes all 7 queries in parallel across both databases (14 total executions)
- Uses ThreadPoolExecutor with up to 4 concurrent connections
- Displays separate result tables for each database
- Live updates as each query completes
- Final summary showing total successful executions

## Files

- `udb.py` - Main Streamlit application
- `UDB KPIs.sql` - SQL queries with date placeholders
- `udb_config.json` - Database configuration for both databases
- `requirements.txt` - Python dependencies (includes ibm-db)

## Troubleshooting

- **Database Connection Issues**: Verify your udb_config.json credentials and network connectivity
- **IBM DB2 Client Issues**: Ensure IBM DB2 client libraries are properly installed and configured
- **Query Errors**: Check the executed SQL in the expandable section for syntax issues
- **Parallel Execution**: The application uses ThreadPoolExecutor for concurrent database connections
- **Performance**: Queries run in parallel for optimal performance across multiple databases
