#!/usr/bin/env python3
"""
Alternative UDB application using ibm_db instead of pyodbc
This uses the same connection method as DB2 client
"""

import streamlit as st
import pandas as pd
import json
import re
from datetime import datetime, date
import os
import threading
import time
import random
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed

# Try to import ibm_db, fall back to demo mode if not available
try:
    import ibm_db
    IBM_DB_AVAILABLE = True
except ImportError:
    IBM_DB_AVAILABLE = False
    st.error("⚠️ ibm_db package not available. Install with: pip install ibm_db")

# Page configuration
st.set_page_config(
    page_title="UDB DB2 KPI Dashboard (IBM_DB)",
    page_icon="🗄️",
    layout="wide"
)

# Demo mode flag - default to True if ibm_db not available
DEMO_MODE = st.sidebar.checkbox("🧪 Demo Mode (Mock Data)", 
                                value=not IBM_DB_AVAILABLE, 
                                help="Use mock data instead of real database connections for testing")

@st.cache_data
def load_config():
    """Load database configuration from udb_config.json"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        st.error("Config file 'udb_config.json' not found. Please create it with your database credentials.")
        st.stop()
    except json.JSONDecodeError:
        st.error("Invalid JSON in udb_config.json file.")
        st.stop()

@st.cache_data
def parse_sql_file():
    """Parse the UDB KPIs.sql file to extract queries and descriptions"""
    try:
        with open('UDB KPIs.sql', 'r') as f:
            content = f.read()
        
        # Split content into blocks separated by empty lines
        blocks = re.split(r'\n\s*\n', content.strip())
        queries = []
        
        for block in blocks:
            if not block.strip():
                continue
                
            lines = block.strip().split('\n')
            description = ""
            sql_lines = []
            
            # Extract description from first comment line
            for line in lines:
                line = line.strip()
                if line.startswith('--') and not description:
                    description = line[2:].strip()
                elif not line.startswith('--') and line:
                    sql_lines.append(line)
            
            if sql_lines:
                sql = '\n'.join(sql_lines)
                if sql.strip():
                    queries.append({
                        'description': description if description else f"Query {len(queries) + 1}",
                        'sql': sql
                    })
        
        return queries
    except FileNotFoundError:
        st.error("SQL file 'UDB KPIs.sql' not found.")
        st.stop()

def create_mock_data(query_description):
    """Create mock data for demo mode"""
    # Generate realistic mock data based on query type
    if "orders sent to PPS" in query_description.lower():
        return pd.DataFrame({
            'PPS_TOTAL': [random.randint(800, 1200)],
            'TOTAL': [random.randint(1000, 1500)],
            'PERCENT': [round(random.uniform(75, 95), 2)]
        })
    elif "orders sent to Scandata" in query_description.lower():
        return pd.DataFrame({
            'SDS_TOTAL': [random.randint(700, 1100)],
            'TOTAL': [random.randint(1000, 1500)],
            'PERCENT': [round(random.uniform(70, 90), 2)]
        })
    elif "Billing" in query_description:
        return pd.DataFrame({
            'SENT': [random.randint(600, 1000)],
            'TOTAL': [random.randint(800, 1200)],
            'PERCENT': [round(random.uniform(65, 85), 2)]
        })
    elif "SAP" in query_description:
        return pd.DataFrame({
            'SENT': [random.randint(500, 900)],
            'TOTAL': [random.randint(700, 1100)],
            'PERCENT': [round(random.uniform(60, 80), 2)]
        })
    elif "> 7 day" in query_description:
        return pd.DataFrame({
            'NOT_SHIPPED': [random.randint(50, 150)] if "Shipped" in query_description else [random.randint(40, 120)],
            'DOWNLOADED': [random.randint(800, 1200)] if "Shipped" in query_description else [random.randint(700, 1100)],
            'PERCENT': [round(random.uniform(5, 15), 2)]
        })
    else:
        # Default mock data
        return pd.DataFrame({
            'VALUE1': [random.randint(100, 500)],
            'VALUE2': [random.randint(200, 600)],
            'PERCENT': [round(random.uniform(50, 90), 2)]
        })

def create_connection_ibmdb(db_config):
    """Create DB2 database connection using ibm_db with SSL support"""
    if not IBM_DB_AVAILABLE:
        st.error("ibm_db package not available")
        return None

    # Connection string formats to try based on your JDBC URL
    # *******************************************************************************************;
    connection_formats = [
        # With SSL enabled (based on your JDBC URL)
        f"DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};SECURITY=SSL;",
        # Alternative SSL format
        f"DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};SSLCONNECTION=true;",
        # Standard format without SSL (fallback)
        f"DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};",
        # With SSL and message retrieval
        f"DATABASE={db_config['database']};HOSTNAME={db_config['host']};PORT={db_config['port']};PROTOCOL=TCPIP;UID={db_config['username']};PWD={db_config['password']};SECURITY=SSL;RETRIEVEMESSAGESFROMSERVERONGETMESSAGE=true;",
    ]

    st.info(f"🔌 Connecting to {db_config['name']} using ibm_db...")
    st.info(f"📍 Connection: {db_config['host']}:{db_config['port']}/{db_config['database']}")
    st.info(f"🔐 Trying SSL-enabled connections based on your JDBC URL format")

    for i, conn_str in enumerate(connection_formats, 1):
        try:
            # Show connection string (with password masked)
            safe_conn_str = conn_str.replace(db_config['password'], '***')
            st.info(f"📝 Method {i}: {safe_conn_str}")

            connection = ibm_db.connect(conn_str, "", "")

            if connection:
                st.success(f"✅ Successfully connected to {db_config['name']} using ibm_db (Method {i})!")

                # Test with a simple query
                try:
                    stmt = ibm_db.exec_immediate(connection, "SELECT CURRENT TIMESTAMP FROM SYSIBM.SYSDUMMY1")
                    result = ibm_db.fetch_tuple(stmt)
                    st.success(f"🧪 Connection test successful: {result[0]}")
                except Exception as test_e:
                    st.warning(f"⚠️ Connection OK but test query failed: {test_e}")

                return connection
            else:
                st.warning(f"❌ Method {i} failed: Connection returned None")

        except Exception as e:
            st.warning(f"❌ Method {i} failed: {str(e)}")

    st.error(f"❌ All connection methods failed for {db_config['name']}")
    return None

def execute_query_ibmdb(connection, sql, from_date, to_date):
    """Execute SQL query using ibm_db"""
    try:
        # Replace date placeholders
        formatted_sql = sql.replace('<from_date>', from_date.strftime('%Y-%m-%d'))
        formatted_sql = formatted_sql.replace('<to_date>', to_date.strftime('%Y-%m-%d'))
        
        # Execute query
        stmt = ibm_db.exec_immediate(connection, formatted_sql)
        
        # Fetch results
        rows = []
        columns = []
        
        # Get column names
        num_cols = ibm_db.num_fields(stmt)
        for i in range(num_cols):
            columns.append(ibm_db.field_name(stmt, i))
        
        # Fetch all rows
        row = ibm_db.fetch_tuple(stmt)
        while row:
            rows.append(row)
            row = ibm_db.fetch_tuple(stmt)
        
        # Convert to DataFrame
        df = pd.DataFrame(rows, columns=columns)
        return df, formatted_sql
    except Exception as e:
        st.error(f"Query execution error: {str(e)}")
        return None, None

def main():
    st.title("🗄️ UDB DB2 KPI Dashboard (IBM_DB)")
    
    # Show demo mode status
    if DEMO_MODE:
        st.warning("🧪 **DEMO MODE ACTIVE** - Using mock data instead of real database connections")
    elif not IBM_DB_AVAILABLE:
        st.error("⚠️ **IBM_DB NOT AVAILABLE** - Install with: pip install ibm_db")
        st.info("💡 Demo mode is automatically enabled")
    
    st.markdown("---")
    
    # Load configuration and SQL queries
    config = load_config()
    queries = parse_sql_file()
    
    # Sidebar for inputs
    st.sidebar.header("Query Parameters")
    
    # Date selectors
    col1, col2 = st.sidebar.columns(2)
    with col1:
        from_date = st.date_input(
            "From Date",
            value=date.today().replace(day=1),
            key="from_date"
        )
    with col2:
        to_date = st.date_input(
            "To Date",
            value=date.today(),
            key="to_date"
        )
    
    # Validate date range
    if from_date > to_date:
        st.sidebar.error("From date must be before or equal to To date")
        return
    
    # Query selection with ALL option
    query_options = ["ALL - Run All Queries"] + [f"{i+1}. {q['description']}" for i, q in enumerate(queries)]
    selected_option = st.sidebar.selectbox(
        "Select KPI Query",
        range(len(query_options)),
        format_func=lambda x: query_options[x]
    )
    
    # Determine if running all queries or single query
    run_all_queries = (selected_option == 0)
    selected_query_idx = selected_option - 1 if not run_all_queries else None
    
    # Display database information
    st.subheader("🗄️ Database Configuration")
    col1, col2 = st.columns(2)
    with col1:
        st.info(f"**Database 1:** {config['database1']['name']}\n\n**Host:** {config['database1']['host']}")
    with col2:
        st.info(f"**Database 2:** {config['database2']['name']}\n\n**Host:** {config['database2']['host']}")
    
    # Show date range
    st.info(f"Date Range: {from_date.strftime('%Y-%m-%d')} to {to_date.strftime('%Y-%m-%d')}")
    
    # Test connection button
    if st.button("🔧 Test IBM_DB Connections", help="Test database connections using ibm_db"):
        st.subheader("🔧 Testing Database Connections with IBM_DB")
        
        if DEMO_MODE:
            st.info("Demo mode is active - skipping real connection tests")
            st.success("✅ Demo mode connections OK")
        elif not IBM_DB_AVAILABLE:
            st.error("❌ ibm_db package not available. Install with: pip install ibm_db")
        else:
            for db_config in config.values():
                st.write(f"**Testing {db_config['name']}:**")
                connection = create_connection_ibmdb(db_config)
                if connection:
                    try:
                        ibm_db.close(connection)
                        st.success(f"✅ {db_config['name']} connection successful")
                    except:
                        pass
                else:
                    st.error(f"❌ {db_config['name']} connection failed")

if __name__ == "__main__":
    main()
