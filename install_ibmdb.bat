@echo off
echo ===============================================
echo Installing IBM DB2 Python Driver (ibm_db)
echo ===============================================
echo.
echo This will install the ibm_db package which uses
echo the same connection method as DB2 client tools.
echo.
echo This should resolve the SQL30081N connection issues
echo you're experiencing with pyodbc.
echo.
pause

echo Installing ibm_db...
pip install ibm_db

echo.
echo Installation complete!
echo.
echo You can now run:
echo   python udb_ibmdb.py
echo.
echo Or use Streamlit:
echo   streamlit run udb_ibmdb.py --server.port 8503
echo.
pause
