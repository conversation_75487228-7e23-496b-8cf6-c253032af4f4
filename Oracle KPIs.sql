-- RTP printpacks merged
select MERGED, TOTAL, round((MERGED/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct BRT_ID) as TOTAL
  from TRI.TRTP_BATCH_ORDER
  where HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
) a1,
(
  select count(distinct batch.BRT_ID) as MERGED
  from TRI.TRTP_BATCH_ORDER batch, TRI.TPRINT_PACK pack
  where batch.HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
    and batch.BRT_ID = pack.BRT_ID
    and pack.RTP_IN = 1
    and pack.FINAL_PP_LOC_TX is not null
) a2;

-- New orders sent to PPS and Scandata
select PROCESSED, TOTAL, round((PROCESSED/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct BRT_ID) as PROCESSED
  from TRI.TMAIL_EVENT_HS hs1
  where EVENT_CD = '1003'
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
    and exists
    (
      select 1
      from TRI.TMAIL_EVENT_HS hs2
      where hs2.BRT_ID = hs1.BRT_ID
        and hs2.EVENT_CD = '1505'
    )
) a1,
(
  select count(distinct BRT_ID) as TOTAL
  from TRI.TMAIL_EVENT_HS hs1
  where EVENT_CD = '1003'
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
) a2;

-- DaaS orders marked Scandata Metered sent to SAP Inventory Decrement
select SENT, TOTAL, round((SENT/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct BRT_ID) as TOTAL
  from TRI.TMAIL_EVENT_HS
  where EVENT_CD in ('6b', '6co')
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
) a1,
(
  select count(distinct BRT_ID) as SENT
  from TRI.TMAIL_EVENT_HS hs1
  where EVENT_CD in ('6b', '6co')
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
    and exists
    (
      select 1
      from TRI.TMAIL_EVENT_HS hs2
      where hs2.BRT_ID = hs1.BRT_ID
        and hs2.EVENT_CD = '6s'
    )
) a2;

-- PPS completed within 7 days (6a)
select PPS_COMPLETE, TOTAL, round((PPS_COMPLETE/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct BRT_ID) as TOTAL
  from TRI.TMAIL_EVENT_HS
  where EVENT_CD in ('1505')
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
) a1,
(
  select count(distinct BRT_ID) as PPS_COMPLETE
  from TRI.TMAIL_EVENT_HS hs1
  where EVENT_CD in ('1505')
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
    and exists
    (
      select 1
      from TRI.TMAIL_EVENT_HS hs2
      where hs2.BRT_ID = hs1.BRT_ID
        and hs2.EVENT_CD = '6a'
        and hs2.HSC_TS < hs1.HSC_TS + interval '7' day
    )
) a2;

-- Scandata completed within 7 days (6b,6co)
select SDS_COMPLETE, TOTAL, round((SDS_COMPLETE/(TOTAL*1.0))*100, 2) as PERCENT
from
(
  select count(distinct BRT_ID) as TOTAL
  from TRI.TMAIL_EVENT_HS
  where EVENT_CD in ('1505')
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
) a1,
(
  select count(distinct BRT_ID) as SDS_COMPLETE
  from TRI.TMAIL_EVENT_HS hs1
  where EVENT_CD in ('1505')
    and HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS')
    and exists
    (
      select 1
      from TRI.TMAIL_EVENT_HS hs2
      where hs2.BRT_ID = hs1.BRT_ID
        and hs2.EVENT_CD in ('6b','6co')
        and hs2.HSC_TS < hs1.HSC_TS + interval '7' day
    )
) a2;

-- p95 of fetching printpacks from carelon
select round(PERCENTILE_CONT(0.05) WITHIN GROUP (ORDER BY PRINTPACK_DOWNLOAD_TIME_NB DESC), 2) as PRINTPACK_FETCH_P95
from TRI.TMAIL_ORDER
where HSC_TS between TO_DATE('<from_date>T00:00:00', 'YYYY-MM-DD"T"HH24:MI:SS') AND TO_DATE('<to_date>T23:59:59', 'YYYY-MM-DD"T"HH24:MI:SS');