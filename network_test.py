#!/usr/bin/env python3
"""
Network connectivity test for DB2 servers
"""

import socket
import json
import sys

def load_config():
    """Load database configuration"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("ERROR: Config file 'udb_config.json' not found.")
        return None

def test_network_connectivity(host, port, timeout=10):
    """Test if we can reach the host and port"""
    try:
        print(f"Testing network connectivity to {host}:{port}...")
        
        # Resolve hostname
        try:
            ip = socket.gethostbyname(host)
            print(f"✅ Hostname resolved: {host} -> {ip}")
        except socket.gaierror as e:
            print(f"❌ Hostname resolution failed: {e}")
            return False
        
        # Test port connectivity
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ Port {port} is open and reachable")
            return True
        else:
            print(f"❌ Port {port} is not reachable (error code: {result})")
            return False
            
    except Exception as e:
        print(f"❌ Network test failed: {e}")
        return False

def main():
    print("=== Network Connectivity Test ===")
    
    config = load_config()
    if not config:
        sys.exit(1)
    
    success_count = 0
    total_count = 0
    
    for db_key, db_config in config.items():
        total_count += 1
        print(f"\n--- Testing {db_key} ({db_config['name']}) ---")
        
        if test_network_connectivity(db_config['host'], db_config['port']):
            success_count += 1
        else:
            print(f"💡 Possible issues:")
            print(f"   - Server might be down")
            print(f"   - Port {db_config['port']} might be blocked by firewall")
            print(f"   - VPN connection might be required")
            print(f"   - Network routing issues")
    
    print(f"\n=== Summary ===")
    print(f"Reachable servers: {success_count}/{total_count}")
    
    if success_count == 0:
        print("\n❌ No servers are reachable. Please check:")
        print("   1. VPN connection (if required)")
        print("   2. Network connectivity")
        print("   3. Firewall settings")
        print("   4. Server availability")
    elif success_count < total_count:
        print("\n⚠️  Some servers are not reachable.")
    else:
        print("\n✅ All servers are reachable!")

if __name__ == "__main__":
    main()
