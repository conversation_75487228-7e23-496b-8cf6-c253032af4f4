#!/usr/bin/env python3
"""
Test the exact connection format that should work with DB2
Since your DB2 client works, this will help us find the right format
"""

import pyodbc
import json
import sys

def load_config():
    """Load database configuration"""
    try:
        with open('udb_config.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("ERROR: Config file 'udb_config.json' not found.")
        return None

def test_exact_db2_format(db_config):
    """Test the exact format that DB2 client uses"""
    print(f"\n=== Testing {db_config['name']} ===")
    print(f"Host: {db_config['host']}")
    print(f"Port: {db_config['port']}")
    print(f"Database: {db_config['database']}")
    print(f"Username: {db_config['username']}")
    
    # Get available drivers
    try:
        drivers = pyodbc.drivers()
        db2_drivers = [d for d in drivers if 'DB2' in d.upper()]
        print(f"Available DB2 drivers: {db2_drivers}")
    except Exception as e:
        print(f"Error getting drivers: {e}")
        return False
    
    if not db2_drivers:
        print("No DB2 drivers found!")
        return False
    
    # Test the most common DB2 connection formats
    # These are the exact formats that typically work with DB2 client
    connection_formats = [
        # Format 1: Standard DB2 ODBC format (most common)
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password}",
        
        # Format 2: Same with trailing semicolon
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};",
        
        # Format 3: Using DBALIAS instead of DATABASE
        "DRIVER={{{driver}}};DBALIAS={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};",
        
        # Format 4: With CONNECTTYPE=1 (remote connection)
        "DRIVER={{{driver}}};DATABASE={database};HOSTNAME={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};CONNECTTYPE=1",
        
        # Format 5: Alternative parameter names
        "DRIVER={{{driver}}};DB={database};HOST={host};PORT={port};PROTOCOL=TCPIP;UID={username};PWD={password};"
    ]
    
    for driver in db2_drivers:
        print(f"\n--- Testing with driver: {driver} ---")
        
        for i, format_template in enumerate(connection_formats, 1):
            try:
                # Build connection string
                conn_str = format_template.format(
                    driver=driver,
                    database=db_config['database'],
                    host=db_config['host'],
                    port=db_config['port'],
                    username=db_config['username'],
                    password=db_config['password']
                )
                
                # Show the connection string (with password masked)
                safe_conn_str = conn_str.replace(db_config['password'], '***')
                print(f"Format {i}: {safe_conn_str}")
                
                # Try to connect
                print(f"  Connecting...", end=" ")
                connection = pyodbc.connect(conn_str, timeout=30)
                print("SUCCESS!")
                
                # Test with a simple query
                print(f"  Testing query...", end=" ")
                cursor = connection.cursor()
                cursor.execute("SELECT CURRENT TIMESTAMP FROM SYSIBM.SYSDUMMY1")
                result = cursor.fetchone()
                print(f"SUCCESS! Result: {result[0]}")
                
                cursor.close()
                connection.close()
                
                print(f"\n🎉 WORKING CONNECTION FOUND!")
                print(f"Driver: {driver}")
                print(f"Format: {i}")
                print(f"Connection String: {safe_conn_str}")
                print(f"\nYou can use this exact format in your application!")
                
                return True
                
            except pyodbc.Error as e:
                error_code = e.args[0] if e.args else 'Unknown'
                error_msg = str(e.args[1]) if len(e.args) > 1 else str(e)
                print(f"FAILED: [{error_code}] {error_msg[:100]}...")
                
                # Show specific error guidance
                if 'SQL30081N' in error_msg:
                    print(f"    → Communication error (this is the error you're seeing)")
                elif 'SQL30082N' in error_msg:
                    print(f"    → Authentication error")
                elif 'SQL1013N' in error_msg:
                    print(f"    → Database not found")
                elif 'IM002' in error_msg:
                    print(f"    → Driver not found")
                    
            except Exception as e:
                print(f"FAILED: {str(e)[:100]}...")
    
    print(f"\n❌ No working connection found for {db_config['name']}")
    return False

def main():
    print("=== DB2 Connection Format Tester ===")
    print("This will test the exact formats that should work with your DB2 setup")
    print("Since your DB2 client works, one of these should work too!")
    
    config = load_config()
    if not config:
        sys.exit(1)
    
    success_count = 0
    for db_config in config.values():
        if test_exact_db2_format(db_config):
            success_count += 1
    
    print(f"\n=== Final Summary ===")
    if success_count > 0:
        print(f"✅ Found {success_count} working connection(s)!")
        print("Use the successful connection string format in your Streamlit app.")
    else:
        print("❌ No connections worked.")
        print("\nPossible issues:")
        print("1. Python ODBC driver might be different from DB2 client")
        print("2. Different authentication method required")
        print("3. Additional connection parameters needed")
        print("4. Environment variables or registry settings missing")
        print("\nNext steps:")
        print("- Check what exact parameters your DB2 client uses")
        print("- Try running this script as administrator")
        print("- Check if there are any DB2 environment variables set")

if __name__ == "__main__":
    main()
